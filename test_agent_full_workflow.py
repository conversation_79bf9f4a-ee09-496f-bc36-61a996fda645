#!/usr/bin/env python3
"""
Comprehensive test of the SecurityAgent using test_article.txt
Tests the full workflow: AT/DoM/PoM generation and Cypher query validation
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from loguru import logger
from src.config import load_config
from src.tools import MCPTools
from src.agent import SecurityAgent
from src.models import ProcessingStatus


async def test_agent_with_article():
    """Test the full agent workflow with the test article"""
    print("🚀 Testing SecurityAgent with test_article.txt")
    print("=" * 60)
    
    try:
        # Load the test article content
        test_article_path = Path("test_article.txt")
        if not test_article_path.exists():
            print("❌ test_article.txt not found!")
            return False
            
        with open(test_article_path, 'r', encoding='utf-8') as f:
            article_content = f.read()
        
        print(f"📄 Loaded test article: {len(article_content)} characters")
        print(f"   Title: {article_content.split('\\n')[0][:80]}...")
        
        # Load configuration
        print("\\n🔧 Loading configuration...")
        config = load_config()
        
        # Initialize tools
        print("🛠️  Initializing tools...")
        tools_config = {
            "article_fetch_timeout": config.processing.article_fetch_timeout,
            "max_content_length": config.processing.max_content_length,
            "cypher_timeout": config.validation.cypher_timeout,
            "mcp": {
                "sequential_thinking_path": config.mcp.sequential_thinking_path,
                "sequential_thinking_args": config.mcp.sequential_thinking_args,
                "memgraph_path": config.mcp.memgraph_path,
                "memgraph_args": config.mcp.memgraph_args,
            },
        }
        
        tools = MCPTools(tools_config)
        await tools.initialize_mcp_clients()
        
        # Initialize agent
        print("🤖 Initializing SecurityAgent...")
        agent = SecurityAgent(config, tools)
        
        # Test 1: Content Assessment
        print("\\n" + "="*60)
        print("📊 PHASE 1: Content Assessment")
        print("="*60)
        
        assessment = await agent._assess_content(article_content)
        
        print(f"✅ Assessment completed:")
        print(f"   Has sufficient content: {assessment.has_sufficient_content}")
        print(f"   Can generate AT: {assessment.can_generate_at}")
        print(f"   Can generate DoM: {assessment.can_generate_dom}")
        print(f"   Can generate PoM: {assessment.can_generate_pom}")
        print(f"   Confidence: {assessment.confidence:.2f}")
        print(f"   Missing aspects: {assessment.missing_aspects}")
        print(f"   Suggested searches: {assessment.suggested_searches}")
        
        if not assessment.has_sufficient_content:
            print("❌ Article doesn't have sufficient content for analysis")
            return False
        
        # Test 2: Attack Technique Generation
        print("\\n" + "="*60)
        print("⚔️  PHASE 2: Attack Technique Generation")
        print("="*60)
        
        if assessment.can_generate_at:
            attack_technique = await agent._generate_attack_technique(article_content)
            
            if attack_technique:
                print(f"✅ Attack Technique generated:")
                print(f"   Name: {attack_technique.name}")
                print(f"   Description: {attack_technique.description[:100]}...")
                print(f"   MITRE Tactics: {attack_technique.mitre_tactics}")
                print(f"   MITRE Technique ID: {attack_technique.mitre_technique_id}")
                print(f"   Target Systems: {attack_technique.target_systems}")
                print(f"   Prerequisites: {attack_technique.prerequisites}")
                print(f"   Confidence Score: {attack_technique.confidence_score}")
                print(f"   Cypher Queries: {len(attack_technique.cypher_queries)}")
                
                # Show Cypher queries
                for i, query in enumerate(attack_technique.cypher_queries, 1):
                    print(f"     Query {i}: {query.name}")
                    print(f"       Description: {query.description}")
                    print(f"       Query: {query.query[:80]}...")
            else:
                print("❌ Failed to generate Attack Technique")
        else:
            print("⚠️  Skipping AT generation - assessment indicates insufficient content")
        
        # Test 3: Detection Model Generation
        print("\\n" + "="*60)
        print("🔍 PHASE 3: Detection Model Generation")
        print("="*60)
        
        if assessment.can_generate_dom:
            detection_model = await agent._generate_detection_model(article_content)
            
            if detection_model:
                print(f"✅ Detection Model generated:")
                print(f"   Name: {detection_model.name}")
                print(f"   Description: {detection_model.description[:100]}...")
                print(f"   Detection Type: {detection_model.detection_type}")
                print(f"   Data Sources: {detection_model.data_sources}")
                print(f"   Expected Output: {detection_model.expected_output[:80]}...")
                print(f"   False Positive Rate: {detection_model.false_positive_rate}")
                print(f"   Confidence Score: {detection_model.confidence_score}")
            else:
                print("❌ Failed to generate Detection Model")
        else:
            print("⚠️  Skipping DoM generation - assessment indicates insufficient content")
        
        # Test 4: Prevention Model Generation
        print("\\n" + "="*60)
        print("🛡️  PHASE 4: Prevention Model Generation")
        print("="*60)
        
        if assessment.can_generate_pom:
            prevention_model = await agent._generate_prevention_model(article_content)
            
            if prevention_model:
                print(f"✅ Prevention Model generated:")
                print(f"   Name: {prevention_model.name}")
                print(f"   Description: {prevention_model.description[:100]}...")
                print(f"   Implementation Complexity: {prevention_model.implementation_complexity}")
                print(f"   Prevention Steps: {len(prevention_model.prevention_steps)} steps")
                for i, step in enumerate(prevention_model.prevention_steps[:3], 1):
                    print(f"     {i}. {step[:60]}...")
                print(f"   Target Platforms: {prevention_model.target_platforms}")
                print(f"   Prerequisites: {prevention_model.prerequisites}")
                print(f"   Effectiveness Rating: {prevention_model.effectiveness_rating}")
                print(f"   Confidence Score: {prevention_model.confidence_score}")
            else:
                print("❌ Failed to generate Prevention Model")
        else:
            print("⚠️  Skipping PoM generation - assessment indicates insufficient content")
        
        # Test 5: Cypher Query Validation
        print("\\n" + "="*60)
        print("🗄️  PHASE 5: Cypher Query Validation")
        print("="*60)
        
        if assessment.can_generate_at and attack_technique and attack_technique.cypher_queries:
            print(f"🔍 Testing {len(attack_technique.cypher_queries)} Cypher queries...")
            
            for i, query in enumerate(attack_technique.cypher_queries, 1):
                print(f"\\n   Query {i}: {query.name}")
                print(f"   Description: {query.description}")
                print(f"   Query: {query.query}")
                
                # Execute the query
                result = await tools.execute_cypher(query.query)
                
                if result["success"]:
                    print(f"   ✅ Query executed successfully")
                    print(f"      Execution time: {result.get('execution_time_ms', 0)}ms")
                    print(f"      Rows returned: {result.get('row_count', 0)}")
                    if result.get('note'):
                        print(f"      Note: {result['note']}")
                else:
                    print(f"   ❌ Query failed: {result.get('error', 'Unknown error')}")
        else:
            print("⚠️  No Cypher queries to validate")
        
        # Test 6: Full Workflow Test
        print("\\n" + "="*60)
        print("🔄 PHASE 6: Full Workflow Test")
        print("="*60)
        
        # Create a mock URL for testing (since we're using file content)
        test_url = "file://test_article.txt"
        
        # Mock the read_article method to return our test content
        original_read_article = tools.read_article
        async def mock_read_article(url):
            if url == test_url:
                return article_content
            return await original_read_article(url)
        tools.read_article = mock_read_article
        
        print(f"🚀 Running full analysis workflow...")
        start_time = datetime.now()
        
        # Run the full analysis
        result = await agent._analyze_article_content(test_url)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        print(f"\\n📊 WORKFLOW RESULTS:")
        print(f"   Status: {result.status}")
        print(f"   Processing time: {processing_time:.2f} seconds")
        print(f"   Errors: {len(result.errors)}")
        print(f"   Warnings: {len(result.warnings)}")
        
        if result.errors:
            print(f"   Error details: {result.errors}")
        
        if result.warnings:
            print(f"   Warning details: {result.warnings}")
        
        # Check generated definitions
        definitions_generated = 0
        if result.attack_technique:
            definitions_generated += 1
            print(f"   ✅ Attack Technique: {result.attack_technique.name}")
            print(f"      Cypher queries: {len(result.attack_technique.cypher_queries)}")
        
        if result.detection_model:
            definitions_generated += 1
            print(f"   ✅ Detection Model: {result.detection_model.name}")
        
        if result.prevention_model:
            definitions_generated += 1
            print(f"   ✅ Prevention Model: {result.prevention_model.name}")
        
        print(f"   📈 Total definitions generated: {definitions_generated}/3")
        
        # Check thought history
        if result.thought_history:
            print(f"   🧠 Thought history: {len(result.thought_history)} thoughts recorded")
        
        # Test 7: Save Results (Optional)
        print("\\n" + "="*60)
        print("💾 PHASE 7: Save Test Results")
        print("="*60)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"test_results_{timestamp}.json"
        
        # Create a serializable version of the result
        result_dict = {
            "test_metadata": {
                "timestamp": timestamp,
                "article_source": "test_article.txt",
                "article_length": len(article_content),
                "processing_time_seconds": processing_time,
            },
            "assessment": {
                "has_sufficient_content": assessment.has_sufficient_content,
                "can_generate_at": assessment.can_generate_at,
                "can_generate_dom": assessment.can_generate_dom,
                "can_generate_pom": assessment.can_generate_pom,
                "confidence": assessment.confidence,
            },
            "results": {
                "status": result.status.value,
                "definitions_generated": definitions_generated,
                "attack_technique_name": result.attack_technique.name if result.attack_technique else None,
                "detection_model_name": result.detection_model.name if result.detection_model else None,
                "prevention_model_name": result.prevention_model.name if result.prevention_model else None,
                "cypher_queries_count": len(result.attack_technique.cypher_queries) if result.attack_technique else 0,
                "errors": result.errors,
                "warnings": result.warnings,
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(result_dict, f, indent=2)
        
        print(f"✅ Test results saved to: {output_file}")
        
        # Final Assessment
        print("\\n" + "="*60)
        print("🎯 FINAL ASSESSMENT")
        print("="*60)
        
        success_criteria = {
            "Content Assessment": assessment.has_sufficient_content,
            "AT Generation": result.attack_technique is not None,
            "DoM Generation": result.detection_model is not None,
            "PoM Generation": result.prevention_model is not None,
            "Cypher Queries": result.attack_technique and len(result.attack_technique.cypher_queries) > 0,
            "No Critical Errors": len(result.errors) == 0,
            "Workflow Completion": result.status == ProcessingStatus.COMPLETE,
        }
        
        passed_tests = sum(success_criteria.values())
        total_tests = len(success_criteria)
        
        print(f"📊 Test Results: {passed_tests}/{total_tests} criteria met")
        
        for criterion, passed in success_criteria.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")
        
        overall_success = passed_tests >= 5  # At least 5/7 criteria should pass
        
        if overall_success:
            print(f"\\n🎉 OVERALL RESULT: SUCCESS!")
            print(f"   The agent successfully processed the test article and generated security definitions.")
        else:
            print(f"\\n⚠️  OVERALL RESULT: PARTIAL SUCCESS")
            print(f"   The agent processed the article but some components need attention.")
        
        # Cleanup
        await tools.cleanup()
        
        return overall_success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the comprehensive agent test"""
    print("🧪 Security Content Analyzer - Full Agent Test")
    print("Testing with test_article.txt (PostgreSQL Cryptominer Attack)")
    print("\\n")
    
    success = await test_agent_with_article()
    
    if success:
        print("\\n✅ All tests completed successfully!")
        sys.exit(0)
    else:
        print("\\n❌ Some tests failed. Check the output above for details.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
