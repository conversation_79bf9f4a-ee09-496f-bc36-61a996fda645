{"test_metadata": {"timestamp": "20250601_183122", "article_source": "test_article.txt", "article_length": 11077, "processing_time_seconds": 0.435742}, "assessment": {"has_sufficient_content": true, "can_generate_at": true, "can_generate_dom": true, "can_generate_pom": true, "confidence": 0.8}, "results": {"status": "complete", "definitions_generated": 3, "attack_technique_name": "Phishing Email Attack", "detection_model_name": "Email Attachment Execution Detection", "prevention_model_name": "Multi-layered Phishing Prevention", "cypher_queries_count": 4, "errors": [], "warnings": []}}